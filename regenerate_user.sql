-- Simple User Creation SQL Script
-- Edit the values in the INSERT statement below and run with: psql -d your_db -f regenerate_user.sql

-- Remove existing user if exists (replace '<EMAIL>' with actual email)
DELETE FROM lumen_team WHERE LOWER(team_email) = LOWER('<EMAIL>');

-- Create new user (edit these values)
INSERT INTO lumen_team (
    team_name,
    team_email, 
    team_password,
    team_disabled,
    team_created_at
) VALUES (
    'Oscar Admin',                    -- Edit: User name
    '<EMAIL>',            -- Edit: User email  
    MD5('2183-0jkl,dsaQW.Puihdjnvs'),            -- Edit: User password
    false,
    current_timestamp
);

-- Show success message
SELECT 'User created successfully!' as message;
SELECT team_id, team_name, team_email, team_created_at 
FROM lumen_team 
WHERE team_email = '<EMAIL>';  -- Edit: Match email above

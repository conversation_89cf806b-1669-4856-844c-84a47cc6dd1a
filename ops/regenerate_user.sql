-- Simple User Creation SQL Script
-- Edit the values below and run with: psql -d your_db -f ops/regenerate_user.sql

-- EDIT THESE VALUES:
\set user_name '<PERSON>'
\set user_email '<EMAIL>'
\set user_password 'password123'

-- Remove existing user if exists
DELETE FROM lumen_team WHERE LOWER(team_email) = LOWER(:'user_email');

-- Create new user
INSERT INTO lumen_team (
    team_name,
    team_email,
    team_password,
    team_disabled,
    team_created_at
) VALUES (
    :'user_name',
    :'user_email',
    MD5(:'user_password'),
    false,
    current_timestamp
);

\echo 'User created!'
\echo 'Email:' :user_email
\echo 'Password:' :user_password

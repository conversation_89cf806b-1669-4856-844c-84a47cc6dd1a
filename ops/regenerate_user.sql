-- Regenerate User SQL Script
-- This script creates or recreates a user (team) with all necessary relationships
-- 
-- Usage: Replace the variables below with your desired values
-- 
-- Variables to customize:
-- @user_name: The display name for the user
-- @user_email: The email address (must be unique)
-- @user_password: The plain text password (will be MD5 hashed)
-- @client_id: The client ID to associate with this user

-- =============================================================================
-- CONFIGURATION - EDIT THESE VALUES
-- =============================================================================

-- Set your user details here
\set user_name '<PERSON>'
\set user_email '<EMAIL>'
\set user_password 'password123'
\set client_id 1

-- =============================================================================
-- SCRIPT EXECUTION - DO NOT EDIT BELOW THIS LINE
-- =============================================================================

-- Start transaction
BEGIN;

-- Remove existing user if exists (and all related data)
DO $$
DECLARE
    existing_team_id INTEGER;
BEGIN
    -- Get existing team ID if user exists
    SELECT team_id INTO existing_team_id 
    FROM lumen_team 
    WHERE LOWER(team_email) = LOWER(:'user_email');
    
    IF existing_team_id IS NOT NULL THEN
        RAISE NOTICE 'Removing existing user with ID: %', existing_team_id;
        
        -- Delete related data first (foreign key constraints)
        DELETE FROM team_clients WHERE team_id = existing_team_id;
        DELETE FROM team_goals WHERE team_id = existing_team_id;
        DELETE FROM lumen_team_metric WHERE metric_team_id = existing_team_id;
        DELETE FROM lumen_team_revenue_number WHERE number_team_id = existing_team_id;
        DELETE FROM lumen_team_selected_initiative WHERE team_id = existing_team_id;
        DELETE FROM lumen_team_selected_challenge WHERE team_id = existing_team_id;
        DELETE FROM team_selected_org_chart WHERE team_id = existing_team_id;
        DELETE FROM self_assessment_answers WHERE team_id = existing_team_id;
        DELETE FROM decision_results WHERE user_id = existing_team_id;
        
        -- Delete the main team record
        DELETE FROM lumen_team WHERE team_id = existing_team_id;
        
        RAISE NOTICE 'Existing user data removed successfully';
    END IF;
END $$;

-- Verify client exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM clients WHERE id = :client_id AND disabled = false) THEN
        RAISE EXCEPTION 'Client with ID % does not exist or is disabled', :client_id;
    END IF;
    RAISE NOTICE 'Client ID % verified', :client_id;
END $$;

-- Create new user
INSERT INTO lumen_team (
    team_name,
    team_email, 
    team_password,
    client_id,
    team_disabled,
    team_created_at
) VALUES (
    :'user_name',
    :'user_email',
    MD5(:'user_password'),
    :client_id,
    false,
    current_timestamp
) RETURNING team_id \gset

-- Display created user info
\echo 'User created successfully!'
\echo 'Team ID:' :team_id
\echo 'Name:' :user_name
\echo 'Email:' :user_email
\echo 'Client ID:' :client_id

-- Create team-client relationship
INSERT INTO team_clients (team_id, client_id)
VALUES (:team_id, :client_id);

\echo 'Team-client relationship created'

-- Create team goals
INSERT INTO team_goals (team_id, client_id)
VALUES (:team_id, :client_id);

\echo 'Team goals created'

-- Create team metrics based on global schemes for this client
INSERT INTO lumen_team_metric (
    metric_team_id,
    metric_name,
    metric_string_value,
    metric_company_value,
    metric_industry_value,
    metric_maximum,
    metric_fixed
)
SELECT 
    :team_id,
    gtms.name,
    '',
    '0',
    '0', 
    100,
    false
FROM global_team_metrics_scheme gtms
WHERE gtms.client_id = :client_id;

-- Get count of metrics created
SELECT COUNT(*) as metrics_count 
FROM lumen_team_metric 
WHERE metric_team_id = :team_id \gset

\echo 'Team metrics created:' :metrics_count

-- Commit transaction
COMMIT;

\echo ''
\echo '=================================='
\echo 'USER REGENERATION COMPLETE!'
\echo '=================================='
\echo 'Login credentials:'
\echo 'Email:' :user_email  
\echo 'Password:' :user_password
\echo 'Team ID:' :team_id
\echo 'Client ID:' :client_id
\echo '=================================='

import { db, disconnect } from '../src/db/index.js';
import { lumenAdmin } from '../src/db/schema.js';
import config from '../config.json' with { type: 'json' };
import { sql } from 'drizzle-orm';

async function regenerateAdmin() {
  try {
    console.log('🔄 Regenerating admin user...');
    
    // Delete existing admin users
    console.log('🗑️  Removing existing admin users...');
    await db.delete(lumenAdmin);
    
    // Insert new admin user
    console.log('👤 Creating new admin user...');
    const [newAdmin] = await db
      .insert(lumenAdmin)
      .values({
        admin_email: config.admin.email,
        admin_password: sql`MD5(${config.admin.password})`,
        admin_created_at: sql`current_timestamp`,
      })
      .returning();
    
    console.log('✅ Admin user regenerated successfully!');
    console.log(`📧 Email: ${newAdmin.admin_email}`);
    console.log(`🆔 ID: ${newAdmin.admin_id}`);
    console.log(`📅 Created: ${newAdmin.admin_created_at}`);
    
  } catch (error) {
    console.error('❌ Error regenerating admin user:', error);
    process.exit(1);
  } finally {
    await disconnect();
  }
}

regenerateAdmin();
